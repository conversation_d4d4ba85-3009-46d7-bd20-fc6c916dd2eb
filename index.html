<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finde Deine ideale Programmierversion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4361ee',
                        secondary: '#3a0ca3',
                        accent: '#4cc9f0',
                        dark: '#1e1e2e',
                        light: '#f8f9fa'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500;700&family=Inter:wght@400;500;700;800&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #1e1e2e 0%, #2a2a3a 100%);
            color: #f8f9fa;
            min-height: 100vh;
        }
        
        h1, h2, h3, h4 {
            font-family: 'Fira Code', monospace;
        }
        
        .version-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
            background: linear-gradient(#2b2b40, #1e1e2e) padding-box, 
                        linear-gradient(135deg, #4361ee, #4cc9f0) border-box;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
        }
        
        .version-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0,0,0,0.2);
        }
        
        .result-badge {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(0.98); }
            50% { transform: scale(1); }
            100% { transform: scale(0.98); }
        }
        
        .progress-bar {
            transition: width 1s ease-in-out;
        }
        
        .recommender-form input:checked + label {
            border-color: #4cc9f0;
            background: rgba(76, 201, 240, 0.1);
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body class="min-h-screen flex flex-col">
    <header class="py-8 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h1 class="text-4xl sm:text-5xl font-bold">
                    <span class="bg-clip-text text-transparent bg-gradient-to-r from-accent to-primary">
                        Finde Deine perfekte Codierungsversion
                    </span>
                </h1>
                <p class="mt-4 text-xl text-gray-300 max-w-3xl mx-auto">
                    Entdecke mit unserem interaktiven Empfehlungstool, welche Version am besten zu Deinem Workflow passt.
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10 shadow-xl">
                    <div class="flex items-center mb-4">
                        <div class="w-14 h-14 flex-shrink-0 flex items-center justify-center bg-primary text-white rounded-xl mr-4">
                            <i class="fas fa-lightbulb text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold">Was ist eine "Version"?</h3>
                    </div>
                    <p class="text-gray-300">
                        Eine "Version" ist ein spezifischer Stand einer Software. Aktuelle Beispiele (Stand Juni 2025):
                    </p>
                    <ul class="mt-3 space-y-2 text-sm">
                        <li class="flex items-start">
                            <i class="fas fa-check text-accent mr-2 mt-1"></i>
                            <div>
                                <strong>Sprachen:</strong><br>
                                Python 3.13 vs. die LTS-Version 3.12
                            </div>
                        </li>
                        
                        <li class="flex items-start">
                            <i class="fas fa-check text-accent mr-2 mt-1"></i>
                            <div>
                                <strong>Frameworks:</strong><br>
                                React 19 mit neuen Compilern vs. React 18
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-accent mr-2 mt-1"></i>
                            <div>
                                <strong>Tools:</strong><br>
                                VS Code 1.95 mit neuen AI-Features
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-accent mr-2 mt-1"></i>
                            <div>
                                <strong>Paketmanager:</strong><br>
                                Node.js 22 (LTS) vs. Node.js 24 (Current)
                            </div>
                        </li>
                    </ul>
                </div>
                
                <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10 shadow-xl">
                    <div class="flex items-center mb-4">
                        <div class="w-14 h-14 flex-shrink-0 flex items-center justify-center rounded-xl mr-4" style="background-color: #3e36c8;">
                            <i class="fas fa-scale-balanced text-2xl text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold">Faktoren der Auswahl</h3>
                    </div>
                    <p class="text-gray-300">Die Wahl hängt von deinen Prioritäten ab. Wäge ab zwischen:</p>
                    <ul class="mt-3 space-y-2 text-sm">
                         <li class="flex items-start">
                            <i class="fas fa-shield-alt text-green-400 mr-2 mt-1"></i>
                            <div>
                                <strong>Stabilität:</strong><br>
                                Wie zuverlässig muss deine Anwendung laufen?
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-rocket text-yellow-400 mr-2 mt-1"></i>
                            <div>
                                <strong>Features:</strong><br>
                                Benötigst du die absolut neuesten Funktionen?
                            </div>
                        </li>
                         <li class="flex items-start">
                            <i class="fas fa-users text-blue-400 mr-2 mt-1"></i>
                            <div>
                                <strong>Community-Support:</strong><br>
                                Wie wichtig sind Foren und Anleitungen?
                            </div>
                        </li>
                         <li class="flex items-start">
                            <i class="fas fa-lock text-red-400 mr-2 mt-1"></i>
                            <div>
                                <strong>Sicherheit:</strong><br>
                                Sind regelmäßige Sicherheitsupdates kritisch?
                            </div>
                        </li>
                    </ul>
                </div>
                
                <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10 shadow-xl">
                    <div class="flex items-center mb-4">
                        <div class="w-14 h-14 flex-shrink-0 flex items-center justify-center bg-secondary text-white rounded-xl mr-4">
                            <i class="fas fa-chart-line text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold">Aktuelle Trends (Q2 2025)</h3>
                    </div>
                     <p class="text-gray-300 mb-4">Laut aktuellen Entwickler-Umfragen:</p>
                    <div class="space-y-3">
                        <div class="text-sm">
                            <div class="flex justify-between mb-1"><span>Neueste stabile Versionen</span><span>72%</span></div>
                            <div class="w-full bg-dark rounded-full h-2"><div class="bg-accent h-2 rounded-full" style="width: 72%"></div></div>
                        </div>
                        <div class="text-sm">
                            <div class="flex justify-between mb-1"><span>LTS-Versionen (Unternehmen)</span><span>21%</span></div>
                            <div class="w-full bg-dark rounded-full h-2"><div class="bg-primary h-2 rounded-full" style="width: 21%"></div></div>
                        </div>
                        <div class="text-sm">
                            <div class="flex justify-between mb-1"><span>Experimentelle Versionen</span><span>5%</span></div>
                            <div class="w-full bg-dark rounded-full h-2"><div class="bg-secondary h-2 rounded-full" style="width: 5%"></div></div>
                        </div>
                         <div class="text-sm">
                            <div class="flex justify-between mb-1"><span>Legacy-Versionen</span><span>2%</span></div>
                            <div class="w-full bg-dark rounded-full h-2"><div class="bg-gray-500 h-2 rounded-full" style="width: 2%"></div></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <main class="flex-grow pb-16">
        <section id="recommender" class="py-12 px-4 sm:px-6">
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl md:text-4xl font-bold">
                        <span class="bg-clip-text text-transparent bg-gradient-to-r from-accent to-primary">
                            Versions-Empfehlungstool
                        </span>
                    </h2>
                </div>
                
                <div class="flex justify-center mb-4">
                    <button id="resetBtn" type="button" class="relative flex items-center bg-dark px-4 py-2 rounded-xl text-white font-medium hover:bg-opacity-90 transition text-sm">
                        <i class="fas fa-rotate mr-2"></i> Zurücksetzen
                    </button>
                </div>

                <div class="w-full bg-dark rounded-full h-2.5 mx-auto">
                    <div id="progressBar" class="progress-bar bg-gradient-to-r from-primary to-accent h-2.5 rounded-full" style="width: 0%"></div>
                </div>
                
                <div id="questionsContainer" class="grid grid-cols-1 gap-8 mt-8">
                    <!-- Questions will be dynamically injected here by JS -->
                </div>

                <div id="navigationContainer" class="mt-8 flex justify-center">
                    <button id="prevBtn" type="button" class="bg-dark text-white px-8 py-3 rounded-xl font-medium hover:bg-opacity-90 transition mr-4">
                        <i class="fas fa-arrow-left mr-2"></i> Vorherige Frage
                    </button>
                    <button id="nextBtn" type="button" class="bg-gradient-to-r from-primary to-accent text-white px-8 py-3 rounded-xl font-medium hover:opacity-90 transition disabled:opacity-30 disabled:cursor-not-allowed">
                        Nächste Frage <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </div>
        </section>
        
        <section id="results" class="py-16 hidden">
             <div class="max-w-4xl mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold">
                        <span class="bg-clip-text text-transparent bg-gradient-to-r from-accent to-primary">
                            Deine ideale Programmierversion
                        </span>
                    </h2>
                    <p class="mt-4 text-xl text-gray-300 max-w-2xl mx-auto">
                        Basierend auf Deinen Präferenzen, hier unsere Empfehlung:
                    </p>
                </div>
                
                <div class="bg-gradient-to-br from-primary/10 to-accent/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                    <div class="flex flex-col md:flex-row items-center">
                        <div id="resultIcon" class="flex-shrink-0 mb-6 md:mb-0 md:mr-8 w-32 h-32 rounded-2xl bg-gradient-to-br from-primary to-accent flex items-center justify-center">
                            <i class="fas fa-medal text-5xl text-white"></i>
                        </div>
                        <div class="text-center md:text-left">
                            <span id="resultBadge" class="result-badge inline-block bg-primary text-white px-4 py-1 rounded-full text-sm font-medium mb-2"></span>
                            <h3 id="resultTitle" class="text-4xl font-bold mb-2"></h3>
                            <p id="resultDescription" class="text-xl text-gray-300 mb-4"></p>
                            <a id="downloadBtn" href="#" target="_blank" class="mt-4 inline-block bg-accent text-white font-bold py-3 px-6 rounded-lg hover:opacity-90 transition-transform hover:scale-105">
                                <i class="fas fa-download mr-2"></i> Jetzt Herunterladen
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="mt-16">
                    <h3 class="text-2xl font-bold mb-8 text-center">Versionsvergleich</h3>
                    
                    <div id="versionComparisonContainer" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Version cards will be inserted here by JS -->
                    </div>
                </div>

                <section id="user-reviews" class="mt-16">
                    <h3 class="text-2xl font-bold mb-8 text-center">Echtzeit Bewertungen</h3>
                    
                    <div class="grid grid-cols-1 gap-6 max-w-4xl mx-auto">
                        <!-- Review 1 -->
                        <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h4 class="font-bold text-lg">Python 3.13</h4>
                                    <div class="flex mt-1">
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star-half-alt text-yellow-400"></i>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-400">Maria K., vor 2 Tagen</span>
                            </div>
                            <p class="text-gray-300 mb-2">"Die neuen JIT-Features machen einen merklichen Unterschied bei meinen Data Science Workflows!"</p>
                        </div>

                        <!-- Review 2 -->
                        <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h4 class="font-bold text-lg">React 19</h4>
                                    <div class="flex mt-1">
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="far fa-star text-yellow-400"></i>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-400">Tom S., vor 1 Woche</span>
                            </div>
                            <p class="text-gray-300 mb-2">"Der neue Compiler ist vielversprechend, aber einige Drittanbieter-Bibliotheken brauchen noch Updates."</p>
                        </div>

                        <!-- Review 3 -->
                        <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h4 class="font-bold text-lg">Node.js 22 (LTS)</h4>
                                    <div class="flex mt-1">
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                        <i class="fas fa-star text-yellow-400"></i>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-400">Andreas M., vor 3 Tagen</span>
                            </div>
                            <p class="text-gray-300 mb-2">"Perfekte Stabilität für unser Produktionssystem. Keine Probleme beim Upgrade von Node 18."</p>
                        </div>
                    </div>

                    <div class="text-center mt-8">
                        <button id="addReviewBtn" class="bg-dark text-white font-bold py-3 px-8 rounded-xl hover:bg-gray-600 transition-all duration-300">
                            <i class="fas fa-plus-circle mr-2"></i> Bewertung hinzufügen
                        </button>
                        
                        <div id="reviewForm" class="mt-6 bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10 max-w-2xl mx-auto hidden">
                            <h4 class="font-bold text-lg mb-4">Deine Bewertung</h4>
                            <div class="mb-4">
                                <label class="block text-sm font-medium mb-2">Produkt</label>
                                <select id="reviewProduct" class="w-full bg-dark border border-white/10 rounded-xl p-3 text-white">
                                    <option value="Python 3.13">Python 3.13</option>
                                    <option value="React 19">React 19</option>
                                    <option value="Node.js 22 (LTS)">Node.js 22 (LTS)</option>
                                    <option value="Vue.js 3.5">Vue.js 3.5</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium mb-2">Name</label>
                                <input id="reviewName" type="text" class="w-full bg-dark border border-white/10 rounded-xl p-3 text-white" placeholder="Dein Name (optional)">
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium mb-2 text-center">Bewertung</label>
                                <div id="ratingStars" class="flex justify-center space-x-2 text-2xl text-gray-400">
                                    <i class="far fa-star cursor-pointer hover:text-yellow-400" data-rating="1"></i>
                                    <i class="far fa-star cursor-pointer hover:text-yellow-400" data-rating="2"></i>
                                    <i class="far fa-star cursor-pointer hover:text-yellow-400" data-rating="3"></i>
                                    <i class="far fa-star cursor-pointer hover:text-yellow-400" data-rating="4"></i>
                                    <i class="far fa-star cursor-pointer hover:text-yellow-400" data-rating="5"></i>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium mb-2">Kommentar</label>
                                <textarea id="reviewComment" class="w-full bg-dark border border-white/10 rounded-xl p-3 text-white" rows="3" placeholder="Teile deine Erfahrungen..."></textarea>
                            </div>
                            <div class="flex justify-end space-x-3">
                                <button id="cancelReviewBtn" class="bg-dark text-white py-2 px-4 rounded-lg hover:bg-gray-600">Abbrechen</button>
                                <button id="submitReviewBtn" class="bg-accent text-white py-2 px-4 rounded-lg hover:opacity-90">Absenden</button>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="testimonials-section" class="py-16 hidden">
                    <div class="max-w-4xl mx-auto px-4">
                        <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">
                            <span class="bg-clip-text text-transparent bg-gradient-to-r from-accent to-primary">
                                What Our Users Say
                            </span>
                        </h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Testimonial 1 -->
                            <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-8 border border-white/10 transform hover:scale-105 transition-transform duration-300">
                                <div class="flex items-center mb-4">
                                    <img src="https://i.pravatar.cc/60?u=user1" alt="User Avatar" class="w-12 h-12 rounded-full mr-4 border-2 border-accent">
                                    <div>
                                        <h4 class="font-bold text-lg text-white">Alex P.</h4>
                                        <p class="text-sm text-gray-400">Full-Stack Developer</p>
                                    </div>
                                </div>
                                <p class="text-gray-300 italic">"This tool finally helped me decide between Node.js LTS and Current for my new project. The breakdown was super clear!"</p>
                            </div>
                            <!-- Testimonial 2 -->
                            <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-8 border border-white/10 transform hover:scale-105 transition-transform duration-300">
                                <div class="flex items-center mb-4">
                                    <img src="https://i.pravatar.cc/60?u=user2" alt="User Avatar" class="w-12 h-12 rounded-full mr-4 border-2 border-accent">
                                    <div>
                                        <h4 class="font-bold text-lg text-white">Sarah K.</h4>
                                        <p class="text-sm text-gray-400">Data Scientist</p>
                                    </div>
                                </div>
                                <p class="text-gray-300 italic">"As someone newer to Python, understanding which version to start with was confusing. The recommender pointed me to the LTS version, which has been perfectly stable for my learning journey."</p>
                            </div>
                             <!-- Testimonial 3 -->
                            <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-8 border border-white/10 transform hover:scale-105 transition-transform duration-300 md:col-span-2">
                                <div class="flex items-center mb-4">
                                    <img src="https://i.pravatar.cc/60?u=user3" alt="User Avatar" class="w-12 h-12 rounded-full mr-4 border-2 border-accent">
                                    <div>
                                        <h4 class="font-bold text-lg text-white">Mike T.</h4>
                                        <p class="text-sm text-gray-400">DevOps Engineer</p>
                                    </div>
                                </div>
                                <p class="text-gray-300 italic">"We use this to help onboard junior developers and explain versioning concepts. The visual comparison is particularly helpful. Great resource!"</p>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="mt-12 text-center">
                    <button id="resetBtnResults" class="bg-dark text-white font-bold py-3 px-8 rounded-xl hover:bg-gray-600 transition-all duration-300">
                        <i class="fas fa-rotate-left mr-2"></i> Erneut versuchen
                    </button>
                </div>
            </div>
        </section>
        
        <section id="faq-section" class="py-16">
            <div class="max-w-4xl mx-auto px-4">
                <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">
                    <span class="bg-clip-text text-transparent bg-gradient-to-r from-accent to-primary">
                        Frequently Asked Questions
                    </span>
                </h2>
                <div class="space-y-6">
                    <!-- FAQ Item 1 -->
                    <details class="group bg-dark/50 backdrop-blur-lg rounded-xl p-6 border border-white/10 cursor-pointer">
                        <summary class="flex justify-between items-center font-medium text-white text-lg group-hover:text-accent transition">
                            Was ist der Unterschied zwischen LTS und Current Version?
                            <i class="fas fa-chevron-down group-open:rotate-180 transition-transform"></i>
                        </summary>
                        <p class="text-gray-300 mt-4 text-sm leading-relaxed">
                            <strong>LTS (Long-Term Support)</strong> Versionen sind auf Stabilität und langfristige Wartung ausgelegt. Sie erhalten Sicherheitsupdates und kritische Bugfixes für einen längeren Zeitraum (oft mehrere Jahre), aber keine neuen Features. Ideal für Produktionsumgebungen und Unternehmensanwendungen.
                            <br><br>
                            <strong>Current (Aktuelle)</strong> Versionen enthalten die neuesten Features und Verbesserungen. Sie sind gut für Entwickler, die mit topaktuellen Technologien arbeiten möchten oder spezifische neue Funktionen benötigen. Sie haben jedoch einen kürzeren Support-Zyklus und können experimenteller sein.
                        </p>
                    </details>
                    <!-- FAQ Item 2 -->
                    <details class="group bg-dark/50 backdrop-blur-lg rounded-xl p-6 border border-white/10 cursor-pointer">
                        <summary class="flex justify-between items-center font-medium text-white text-lg group-hover:text-accent transition">
                            Wie oft sollte ich meine Programmierwerkzeuge aktualisieren?
                            <i class="fas fa-chevron-down group-open:rotate-180 transition-transform"></i>
                        </summary>
                        <p class="text-gray-300 mt-4 text-sm leading-relaxed">
                            Das hängt von deinen Bedürfnissen ab:
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li><strong>Für maximale Stabilität (z.B. Firmensysteme):</strong> Halte dich an LTS-Versionen und aktualisiere nur, wenn es notwendig ist oder eine neue LTS-Version gut etabliert ist.</li>
                                <li><strong>Für Hobbyprojekte und Lernen:</strong> Regelmäßige Updates (monatlich oder vierteljährlich) sind gut, um neue Features kennenzulernen.</li>
                                <li><strong>Für professionelle Entwicklung mit neuesten Features:</strong> Aktualisiere häufiger, aber teste immer gründlich, besonders bei Teamprojekten.</li>
                            </ul>
                            Wichtig ist, immer die Release Notes zu lesen und Backups zu erstellen, bevor du größere Updates durchführst.
                        </p>
                    </details>
                    <!-- FAQ Item 3 -->
                    <details class="group bg-dark/50 backdrop-blur-lg rounded-xl p-6 border border-white/10 cursor-pointer">
                        <summary class="flex justify-between items-center font-medium text-white text-lg group-hover:text-accent transition">
                            Kann ich mehrere Versionen einer Sprache/Tools parallel nutzen?
                            <i class="fas fa-chevron-down group-open:rotate-180 transition-transform"></i>
                        </summary>
                        <p class="text-gray-300 mt-4 text-sm leading-relaxed">
                            Ja, das ist oft möglich und sogar empfehlenswert, besonders wenn du an verschiedenen Projekten mit unterschiedlichen Anforderungen arbeitest. Tools wie <code>nvm</code> (für Node.js), <code>pyenv</code> (für Python) oder Docker-Container ermöglichen es dir, verschiedene Versionen isoliert voneinander zu installieren und zu verwalten. So kannst du pro Projekt die passende Version verwenden, ohne Konflikte zu erzeugen.
                        </p>
                    </details>
                     <!-- FAQ Item 4 -->
                    <details class="group bg-dark/50 backdrop-blur-lg rounded-xl p-6 border border-white/10 cursor-pointer">
                        <summary class="flex justify-between items-center font-medium text-white text-lg group-hover:text-accent transition">
                            Wo finde ich Informationen zu den neuesten Versionen?
                            <i class="fas fa-chevron-down group-open:rotate-180 transition-transform"></i>
                        </summary>
                        <p class="text-gray-300 mt-4 text-sm leading-relaxed">
                            Die besten Quellen sind die offiziellen Webseiten der jeweiligen Technologie:
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li>Python: <a href="https://www.python.org" target="_blank" rel="noopener" class="text-accent hover:underline">python.org</a></li>
                                <li>Node.js: <a href="https://nodejs.org" target="_blank" rel="noopener" class="text-accent hover:underline">nodejs.org</a></li>
                                <li>React: <a href="https://react.dev" target="_blank" rel="noopener" class="text-accent hover:underline">react.dev</a></li>
                                <li>Vue.js: <a href="https://vuejs.org" target="_blank" rel="noopener" class="text-accent hover:underline">vuejs.org</a></li>
                            </ul>
                            Viele Technologien haben auch Blogs, Newsletter oder Community-Foren, in denen Updates und neue Releases angekündigt werden.
                        </p>
                    </details>
                </div>
            </div>
        </section>

        <section id="community-insights" class="py-16 hidden">
            <div class="max-w-7xl mx-auto px-4">
                <h2 class="text-3xl md:text-4xl font-bold text-center mb-16">
                    <span class="bg-clip-text text-transparent bg-gradient-to-r from-accent to-primary">
                        Community Insights
                    </span>
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10 hover:border-primary transition">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3">
                                <i class="fab fa-python"></i>
                            </div>
                            <div>
                                <h3 class="font-bold">Python</h3>
                                <div class="flex items-center text-yellow-400">
                                    <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star-half-alt"></i>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-400 mb-4 text-sm">Python 3.13 bringt JIT-Verbesserungen für mehr Geschwindigkeit.</p>
                        <div class="flex justify-between text-xs font-mono">
                            <span>LTS: 3.12</span>
                            <span class="text-green-500 font-medium">Aktuell: 3.13</span>
                        </div>
                    </div>
                    
                    <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10 hover:border-primary transition">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full bg-blue-500/10 text-blue-500 flex items-center justify-center mr-3">
                                <i class="fab fa-js"></i>
                            </div>
                            <div>
                                <h3 class="font-bold">Node.js</h3>
                                <div class="flex items-center text-yellow-400">
                                    <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-400 mb-4 text-sm">Node.js 22 (LTS) verbessert die Stabilität und Performance von Web-Servern.</p>
                        <div class="flex justify-between text-xs font-mono">
                            <span>LTS: 22</span>
                            <span class="text-green-500 font-medium">Aktuell: 24</span>
                        </div>
                    </div>
                    
                    <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10 hover:border-primary transition">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full bg-purple-500/10 text-purple-500 flex items-center justify-center mr-3">
                                <i class="fab fa-react"></i>
                            </div>
                            <div>
                                <h3 class="font-bold">React</h3>
                                <div class="flex items-center text-yellow-400">
                                    <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star-half-alt"></i>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-400 mb-4 text-sm">React 19 führt den neuen Compiler ein, der die Performance optimiert.</p>
                         <div class="flex justify-between text-xs font-mono">
                            <span>LTS: 18</span>
                            <span class="text-green-500 font-medium">Aktuell: 19</span>
                        </div>
                    </div>
                    
                    <div class="bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10 hover:border-primary transition">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full bg-green-500/10 text-green-500 flex items-center justify-center mr-3">
                                <i class="fab fa-vuejs"></i>
                            </div>
                            <div>
                                <h3 class="font-bold">Vue.js</h3>
                                 <div class="flex items-center text-yellow-400">
                                    <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="far fa-star"></i>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-400 mb-4 text-sm">Vue 3.5 "Tusun" verbessert die Reaktivität und Developer Experience.</p>
                        <div class="flex justify-between text-xs font-mono">
                            <span>LTS: 3.4</span>
                            <span class="text-green-500 font-medium">Aktuell: 3.5</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentStep = 1;
            let totalSteps = 7; // Updated total steps
            const questionsContainer = document.getElementById('questionsContainer');
            const navigationContainer = document.getElementById('navigationContainer');
            const recommenderSection = document.getElementById('recommender');
            const resultsSection = document.getElementById('results');
            const testimonialsSection = document.getElementById('testimonials-section'); // Added
            const communityInsightsSection = document.getElementById('community-insights');
            const resetBtn = document.getElementById('resetBtn');
            const resetBtnResults = document.getElementById('resetBtnResults');
            const progressBar = document.getElementById('progressBar');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            const resultIcon = document.getElementById('resultIcon');
            const resultBadge = document.getElementById('resultBadge');
            const resultTitle = document.getElementById('resultTitle');
            const resultDescription = document.getElementById('resultDescription');
            const downloadBtn = document.getElementById('downloadBtn');
            const versionComparisonContainer = document.getElementById('versionComparisonContainer');

            const questions = [
                { name: 'experience', title: 'Wie erfahren bist Du?', description: 'Wähle die Option, die Deine Programmierkenntnisse am besten beschreibt.', options: [ { value: 'beginner', title: 'Anfänger', description: 'Ich starte gerade.', icon: 'fas fa-seedling' }, { value: 'intermediate', title: 'Fortgeschritten', description: 'Ich kenne die Grundlagen.', icon: 'fas fa-rocket' }, { value: 'advanced', title: 'Erfahren', description: 'Ich baue komplexe Projekte.', icon: 'fas fa-crown' }, { value: 'expert', title: 'Experte', description: 'Ich bin Profi-Entwickler.', icon: 'fas fa-brain' } ] },
                { name: 'stability', title: 'Wie wichtig ist Dir Stabilität?', description: 'Wähle, wie sehr Du auf stabile, getestete Versionen setzt.', options: [ { value: 'critical', title: 'Sehr Wichtig', description: 'Stabilität vor allem.', icon: 'fas fa-shield-alt' }, { value: 'balanced', title: 'Ausgewogen', description: 'Ein guter Mix aus beidem.', icon: 'fas fa-balance-scale' }, { value: 'features', title: 'Neue Features', description: 'Ich will das Neueste.', icon: 'fas fa-flask' } ] },
                { name: 'purpose', title: 'Für welchen Zweck programmierst Du?', description: 'Wähle den Hauptgrund für Deine Entwicklungsarbeit.', options: [ { value: 'personal', title: 'Persönliche Projekte', description: 'Hobby & Experimente.', icon: 'fas fa-user' }, { value: 'startup', title: 'Startup/<br>Unternehmen', description: 'Produktentwicklung.', icon: 'fas fa-lightbulb' }, { value: 'enterprise', title: 'Großunternehmen', description: 'Kritische Systeme.', icon: 'fas fa-building' }, { value: 'education', title: 'Ausbildung/<br>Lehre', description: 'Schule oder Uni.', icon: 'fas fa-graduation-cap' } ] },
                { name: 'technology', title: 'Welche Technologien nutzt Du hauptsächlich?', description: 'Wähle Deine primären Programmiersprachen/Frameworks.', options: [ { value: 'web', title: 'Web Entwicklung', description: 'HTML/CSS, JS, etc.', icon: 'fab fa-html5' }, { value: 'backend', title: 'Backend/APIs', description: 'Node.js, Python, etc.', icon: 'fas fa-server' }, { value: 'mobile', title: 'Mobile Apps', description: 'React Native, Flutter, etc.', icon: 'fas fa-mobile-alt' }, { value: 'data', title: 'Data Science', description: 'Python, R, ML, etc.', icon: 'fas fa-database' } ] },
                { name: 'updates', title: 'Wie häufig aktualisierst Du Deine Tools?', description: 'Dein bevorzugter Update-Rhythmus.', options: [ { value: 'monthly', title: 'Monatlich', description: 'Bleibe stets aktuell.', icon: 'fas fa-calendar-alt' }, { value: 'quarterly', title: 'Vierteljährlich', description: 'Stabile Updates.', icon: 'fas fa-calendar' }, { value: 'yearly', title: 'Jährlich', description: 'Nur große Updates.', icon: 'fas fa-calendar-day' }, { value: 'lts', title: 'Nur LTS', description: 'Langzeit-Support.', icon: 'fas fa-lock' } ] },
                { name: 'team', title: 'Arbeitest Du im Team?', description: 'Wie wichtig ist Kompatibilität mit anderen?', options: [ { value: 'solo', title: 'Einzelkämpfer:in', description: 'Ich arbeite alleine.', icon: 'fas fa-user' }, { value: 'small', title: 'Kleines Team', description: '2-5 Entwickler:innen.', icon: 'fas fa-users' }, { value: 'medium', title: 'Mittleres Team', description: '6-15 Entwickler:innen.', icon: 'fas fa-users-cog' }, { value: 'large', title: 'Großes Unternehmen', description: 'Enterprise-Umgebung.', icon: 'fas fa-building' } ] },
                { name: 'superpower', title: 'Wähle Deine Coding-Superkraft!', description: 'Wenn Du eine Superkraft beim Programmieren hättest, welche wäre es?', options: [ { value: 'bug_squasher', title: 'Instant Bug Fixing', description: 'Fehler verschwinden magisch.', icon: 'fas fa-bug-slash' }, { value: 'code_telepathy', title: 'Code Telepathie', description: 'Verstehe Code sofort.', icon: 'fas fa-head-side-brain' }, { value: 'infinite_docs', title: 'Unendliche Doku', description: 'Nie wieder suchen.', icon: 'fas fa-book-open-reader' }, { value: 'perfect_refactor', title: 'Perfektes Refactoring', description: 'Optimaler Code, immer.', icon: 'fas fa-wand-magic-sparkles' } ] }
            ];
            
            const answers = {};

            const versionData = {
                python: { current: "3.13", lts: "3.12", previous: "3.11", downloadUrl: "https://www.python.org/downloads/" },
                node: { current: "24", lts: "22", previous: "20", downloadUrl: "https://nodejs.org/en/download" },
                react: { current: "19", lts: "18", previous: "18", downloadUrl: "https://react.dev/learn" },
                vue: { current: "3.5", lts: "3.4", previous: "2.7", downloadUrl: "https://vuejs.org/guide/quick-start.html" },
                java: { current: "23", lts: "21", previous: "17", downloadUrl: "https://www.oracle.com/java/technologies/downloads/" }
            };

            function renderQuestion() {
                const question = questions[currentStep - 1];
                let questionHTML = `
                    <div class="question-card bg-dark rounded-2xl p-6" data-step="${currentStep}">
                        <div class="flex items-start mb-6">
                            <span class="flex-shrink-0 w-12 h-12 bg-primary/20 text-primary rounded-xl flex items-center justify-center font-bold text-xl mr-4">${currentStep}</span>
                            <div>
                                <h3 class="text-2xl font-bold text-white">${question.title}</h3>
                                <p class="text-gray-400 mt-1">${question.description}</p>
                            </div>
                        </div>
                        <div class="recommender-form grid grid-cols-1 sm:grid-cols-2 md:grid-cols-${question.options.length > 3 ? 4 : 3} gap-4 mt-6">
                `;
                question.options.forEach(opt => {
                    const isChecked = answers[question.name] === opt.value;
                    questionHTML += `
                        <input type="radio" id="${question.name}-${opt.value}" name="${question.name}" value="${opt.value}" class="hidden" ${isChecked ? 'checked' : ''}>
                        <label for="${question.name}-${opt.value}" class="flex flex-col items-center p-6 bg-dark border border-white/10 rounded-2xl hover:border-accent cursor-pointer transition">
                            <div class="mb-4 text-primary text-3xl"><i class="${opt.icon}"></i></div>
                            <h4 class="text-xl font-bold mb-2">${opt.title}</h4>
                            <p class="text-gray-400 text-center text-sm">${opt.description}</p>
                        </label>
                    `;
                });
                questionHTML += `</div></div>`;
                questionsContainer.innerHTML = questionHTML;

                document.querySelectorAll('.recommender-form input[type="radio"]').forEach(input => {
                    input.addEventListener('change', () => {
                        const currentQuestionName = questions[currentStep - 1].name;
                        answers[currentQuestionName] = document.querySelector(`input[name="${currentQuestionName}"]:checked`).value;
                        updateUI();
                    });
                });
                
                updateUI();
            }

            function updateUI() {
                const progressPercent = ((currentStep - 1) / totalSteps) * 100;
                
                // Fix: Direkt die Breite setzen statt CSS-Variable
                progressBar.style.width = `${progressPercent}%`;

                prevBtn.disabled = currentStep === 1;
                prevBtn.classList.toggle('opacity-30', prevBtn.disabled);
                prevBtn.classList.toggle('cursor-not-allowed', prevBtn.disabled);
                
                const hasSelection = !!answers[questions[currentStep - 1].name];
                nextBtn.disabled = !hasSelection;
                nextBtn.classList.toggle('opacity-30', !hasSelection);
                nextBtn.classList.toggle('cursor-not-allowed', !hasSelection);
                
                nextBtn.innerHTML = currentStep === totalSteps
                    ? 'Ergebnis sehen <i class="fas fa-medal ml-2"></i>'
                    : 'Nächste Frage <i class="fas fa-arrow-right ml-2"></i>';
            }
            
            function showResults() {
                recommenderSection.classList.add('hidden');
                resultsSection.classList.remove('hidden');
                testimonialsSection.classList.remove('hidden'); // Show testimonials
                communityInsightsSection.classList.remove('hidden');
                updateResultsContent();
                window.scrollTo({ top: resultsSection.offsetTop - 40, behavior: 'smooth' });
            }
            
            function resetQuiz() {
                Object.keys(answers).forEach(key => delete answers[key]);
                recommenderSection.classList.remove('hidden');
                resultsSection.classList.add('hidden');
                testimonialsSection.classList.add('hidden'); // Hide testimonials
                communityInsightsSection.classList.add('hidden');
                currentStep = 1;
                renderQuestion();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
            
            prevBtn.addEventListener('click', () => {
                if (currentStep > 1) {
                    currentStep--;
                    renderQuestion();
                }
            });

            nextBtn.addEventListener('click', () => {
                if (currentStep < totalSteps) {
                    currentStep++;
                    renderQuestion();
                } else {
                    showResults();
                }
            });

            resetBtn.addEventListener('click', resetQuiz);
            resetBtnResults.addEventListener('click', resetQuiz);
            
            function updateResultsContent() {
                let techKey = 'python';
                if (answers.technology === 'web') techKey = 'react';
                else if (answers.technology === 'backend') techKey = 'node';
                else if (answers.technology === 'mobile') techKey = 'react';
                else if (answers.technology === 'data') techKey = 'python';

                let versionType = 'lts';
                let title = "LTS-Version";
                let description = "Die perfekte Balance zwischen Stabilität und neuen Features.";
                let iconClass = "fas fa-check-circle text-5xl text-white";

                if (answers.stability === 'features' && answers.purpose === 'personal') {
                    versionType = 'current';
                    title = "Aktuellste Version";
                    description = "Ideal für Experimente mit den neuesten Funktionen.";
                    iconClass = "fas fa-rocket text-5xl text-white";
                } else if (answers.purpose === 'enterprise' || answers.team === 'large' || answers.stability === 'critical') {
                    versionType = 'lts';
                    title = "Unternehmens-LTS";
                    description = "Maximale Stabilität und Langzeit-Support für kritische Systeme.";
                    iconClass = "fas fa-shield-alt text-5xl text-white";
                } else if (answers.experience === 'beginner') {
                     versionType = 'lts';
                     title = "Stabile LTS-Version";
                     description = "Ein verlässlicher Startpunkt für deine Lernreise.";
                     iconClass = "fas fa-graduation-cap text-5xl text-white";
                }

                const techData = versionData[techKey];
                const techName = techKey.charAt(0).toUpperCase() + techKey.slice(1);
                
                resultBadge.textContent = `Empfehlung für ${techName}`;
                resultTitle.textContent = `${title} (${techData[versionType]})`;
                
                let fullDescription = description;
                if (answers.superpower === 'bug_squasher') {
                    fullDescription += " <br><strong class='text-accent'>Zusatz-Flair:</strong> Mit Deiner 'Instant Bug Fixing'-Superkraft bist Du bereit, jede Herausforderung anzugehen!";
                } else if (answers.superpower === 'perfect_refactor') {
                    fullDescription += " <br><strong class='text-accent'>Zusatz-Flair:</strong> Deine Fähigkeit zum 'Perfekten Refactoring' wird Dir helfen, eleganten Code zu schreiben!";
                } else if (answers.superpower === 'code_telepathy') {
                    fullDescription += " <br><strong class='text-accent'>Zusatz-Flair:</strong> 'Code Telepathie'? Du wirst die Tiefen jeder Codebasis mühelos meistern!";
                } else if (answers.superpower === 'infinite_docs') {
                    fullDescription += " <br><strong class='text-accent'>Zusatz-Flair:</strong> Mit 'Unendlicher Doku' an Deiner Seite ist kein Problem zu komplex!";
                }
                resultDescription.innerHTML = fullDescription; // Use innerHTML to render the <br> and <strong>

                resultIcon.innerHTML = `<i class="${iconClass}"></i>`;
                downloadBtn.href = techData.downloadUrl;
                
                renderVersionComparison(techKey);
            }
            
            function renderVersionComparison(techKey) {
                const techData = versionData[techKey];
                const techName = techKey.charAt(0).toUpperCase() + techKey.slice(1);

                versionComparisonContainer.innerHTML = `
                    <div class="version-card p-6">
                        <div class="flex-grow">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-bold text-xl">${techName} ${techData.current}</h4>
                                <span class="bg-yellow-500/10 text-yellow-500 px-2 py-1 rounded-lg text-sm">Neueste</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex"><i class="fas fa-plus-circle text-green-500 mt-1 mr-2"></i>Top-Features</li>
                                <li class="flex"><i class="fas fa-minus-circle text-red-500 mt-1 mr-2"></i>Mögliche Instabilität</li>
                            </ul>
                        </div>
                        <div class="mt-4 text-center">
                            <a href="${techData.downloadUrl}" target="_blank" class="inline-block bg-dark text-white text-sm font-bold py-2 px-4 rounded-lg hover:bg-opacity-90 transition-all duration-300">Download</a>
                        </div>
                    </div>
                    <div class="version-card p-6 scale-105 shadow-lg">
                         <div class="flex-grow">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-bold text-xl">${techName} ${techData.lts}</h4>
                                <span class="bg-green-500/10 text-green-500 px-2 py-1 rounded-lg text-sm">Empfohlen</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex"><i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>Beste Balance</li>
                                <li class="flex"><i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>Langzeit-Support</li>
                            </ul>
                        </div>
                        <div class="mt-4 text-center">
                            <a href="${techData.downloadUrl}" target="_blank" class="inline-block bg-accent text-white text-sm font-bold py-2 px-4 rounded-lg hover:opacity-90 transition-all duration-300">Download</a>
                        </div>
                    </div>
                    <div class="version-card p-6">
                        <div class="flex-grow">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-bold text-xl">${techName} ${techData.previous}</h4>
                                <span class="bg-blue-500/10 text-blue-500 px-2 py-1 rounded-lg text-sm">Stabil</span>
                            </div>
                            <ul class="space-y-3 text-gray-300">
                                <li class="flex"><i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>Maximale Stabilität</li>
                                <li class="flex"><i class="fas fa-exclamation-triangle text-yellow-500 mt-1 mr-2"></i>Veraltete Features</li>
                            </ul>
                        </div>
                         <div class="mt-4 text-center">
                            <a href="${techData.downloadUrl}" target="_blank" class="inline-block bg-dark text-white text-sm font-bold py-2 px-4 rounded-lg hover:bg-opacity-90 transition-all duration-300">Download</a>
                        </div>
                    </div>
                `;
            }

            // Initial render
            renderQuestion();

            // Review functionality
            const addReviewBtn = document.getElementById('addReviewBtn');
            const reviewForm = document.getElementById('reviewForm');
            const cancelReviewBtn = document.getElementById('cancelReviewBtn');
            const submitReviewBtn = document.getElementById('submitReviewBtn');
            const ratingStars = document.getElementById('ratingStars');
            const reviewsContainer = document.querySelector('.grid.grid-cols-1.gap-6.max-w-4xl.mx-auto');
            
            let currentRating = 0;
            
            // Show/hide review form
            addReviewBtn.addEventListener('click', () => {
                reviewForm.classList.remove('hidden');
                addReviewBtn.classList.add('hidden');
            });
            
            cancelReviewBtn.addEventListener('click', () => {
                reviewForm.classList.add('hidden');
                addReviewBtn.classList.remove('hidden');
                resetReviewForm();
            });
            
            // Star rating functionality
            ratingStars.querySelectorAll('i').forEach(star => {
                star.addEventListener('click', () => {
                    const rating = parseInt(star.getAttribute('data-rating'));
                    currentRating = rating;
                    
                    // Update stars visual
                    ratingStars.querySelectorAll('i').forEach((s, index) => {
                        if (index < rating) {
                            s.classList.remove('far');
                            s.classList.add('fas');
                            s.classList.add('text-yellow-400');
                        } else {
                            s.classList.add('far');
                            s.classList.remove('fas');
                            s.classList.remove('text-yellow-400');
                        }
                    });
                });
            });
            
            // Submit review
            submitReviewBtn.addEventListener('click', () => {
                const product = document.getElementById('reviewProduct').value;
                const comment = document.getElementById('reviewComment').value;
                const name = document.getElementById('reviewName').value.trim() || 'Anonym';
                
                if (currentRating === 0 || !comment.trim()) {
                    alert('Bitte gib eine Bewertung und einen Kommentar ab.');
                    return;
                }
                
                // Create new review
                const newReview = document.createElement('div');
                newReview.className = 'bg-dark/50 backdrop-blur-lg rounded-2xl p-6 border border-white/10';
                
                // Get current date
                const now = new Date();
                const dateStr = 'heute';
                
                // Create stars HTML
                let starsHTML = '';
                for (let i = 1; i <= 5; i++) {
                    if (i <= currentRating) {
                        starsHTML += '<i class="fas fa-star text-yellow-400"></i>';
                    } else if (i === currentRating + 0.5) {
                        starsHTML += '<i class="fas fa-star-half-alt text-yellow-400"></i>';
                    } else {
                        starsHTML += '<i class="far fa-star text-yellow-400"></i>';
                    }
                }
                
                // Set review content
                newReview.innerHTML = `
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h4 class="font-bold text-lg">${product}</h4>
                            <div class="flex mt-1">
                                ${starsHTML}
                            </div>
                        </div>
                        <span class="text-sm text-gray-400">${name}, ${dateStr}</span>
                    </div>
                    <p class="text-gray-300 mb-2">"${comment}"</p>
                `;
                
                // Add to page
                reviewsContainer.prepend(newReview);
                
                // Reset form and hide
                resetReviewForm();
                reviewForm.classList.add('hidden');
                addReviewBtn.classList.remove('hidden');
            });
            
            function resetReviewForm() {
                document.getElementById('reviewProduct').selectedIndex = 0;
                document.getElementById('reviewComment').value = '';
                document.getElementById('reviewName').value = '';
                currentRating = 0;
                
                ratingStars.querySelectorAll('i').forEach(star => {
                    star.classList.add('far');
                    star.classList.remove('fas');
                    star.classList.remove('text-yellow-400');
                });
            }
        });
    </script>
    
    <footer class="bg-dark/80 backdrop-blur-lg border-t border-white/10 py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="md:flex md:items-center md:justify-between">
                <div class="text-center md:text-left text-gray-400 text-sm">
                    &copy; 2025 Code-Version-Empfehlungen. Alle Rechte vorbehalten.
                </div>
                <div class="mt-4 md:mt-0 flex justify-center space-x-6">
                    <a href="https://www.youtube.com/watch?v=NClmyC6olC0" target="_blank" rel="noopener" class="text-gray-400 hover:text-red-500 transition-colors">
                        <span class="sr-only">YouTube</span>
                        <i class="fab fa-youtube text-xl"></i>
                        <span class="ml-1 text-sm hidden md:inline">Top Programmiersprachen</span>
                    </a>
                    <a href="https://www.youtube.com/watch?v=P_4QGh5HFiw" target="_blank" rel="noopener" class="text-gray-400 hover:text-red-500 transition-colors">
                        <span class="sr-only">YouTube</span>
                        <i class="fab fa-youtube text-xl"></i>
                        <span class="ml-1 text-sm hidden md:inline">LTS erklärt</span>
                    </a>
                    <a href="https://www.youtube.com/watch?v=EUJlVYggR1Y" target="_blank" rel="noopener" class="text-gray-400 hover:text-red-500 transition-colors">
                        <span class="sr-only">YouTube</span>
                        <i class="fab fa-youtube text-xl"></i>
                        <span class="ml-1 text-sm hidden md:inline">Start with VS Code</span>
                    </a>
                </div>
            </div>
            
            <!-- Rechtliche Links -->
            <div class="mt-6 pt-6 border-t border-white/10">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Impressum -->
                    <div>
                        <h4 class="text-lg font-bold text-white mb-3">Impressum</h4>
                        <div class="text-gray-400 text-sm space-y-2">
                            <p><strong>Code-Version-Empfehlungen</strong></p>
                            <p>Tel: +49 179 7867936</p>
                            <p>E-Mail: <EMAIL></p>
                            <p>Projektleitung: Open Source Community</p>
                        </div>
                    </div>
                    
                    <!-- Datenschutz -->
                    <div>
                        <h4 class="text-lg font-bold text-white mb-3">Datenschutzerklärung</h4>
                        <div class="text-gray-400 text-sm space-y-2">
                            <p>Wir nehmen den Schutz Ihrer persönlichen Daten sehr ernst.</p>
                            <p>Die Datenverarbeitung erfolgt durch den Websitebetreiber.</p>
                            <p>Ihre Daten werden nur zur Bereitstellung der Website und zur Analyse des Nutzerverhaltens verwendet.</p>
                            <p>Sie haben jederzeit das Recht auf Auskunft, Berichtigung oder Löschung Ihrer Daten.</p>
                            <p>Für detaillierte Informationen kontaktieren Sie uns <NAME_EMAIL></p>
                        </div>
                    </div>
                    
                    <!-- Rechtliche Hinweise -->
                    <div>
                        <h4 class="text-lg font-bold text-white mb-3">Rechtliche Hinweise</h4>
                        <div class="text-gray-400 text-sm space-y-2">
                            <p><strong>Haftungsausschluss:</strong> Trotz sorgfältiger Kontrolle übernehmen wir keine Haftung für die Inhalte externer Links.</p>
                            <p><strong>Urheberrecht:</strong> Alle Inhalte dieser Website unterliegen dem deutschen Urheberrecht.</p>
                            <p><strong>Bildnachweise:</strong> Alle verwendeten Bilder sind lizenzfrei oder mit entsprechender Genehmigung verwendet.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
