/*  Ein Kommentar in CSS */

/*  
	Der * definiert eine globale Variable.
	Den sogenannten "global reset".
	Hier werden alle Elemente angesprochen, die später nicht anders definiert werden. 
	Früher verwendet um die verschiedenen Browser Versionen auf den gleich Standard zu bringen. 
	Oft wird auch heute noch, besonders bei Word Press Templates oder bei Bootstrap eine normalize.css eingesetzt, die dies für ALLE html Elemente durchführt.

	In diesem Fall werden zwei Sachen erreicht:
	margin und padding  werden auf 0 zurück gesetzt, um dann diese Eigenschaften mit eigenen Werten zu versehen.
	Die Schrifttype wird mit font-family auf eine Standard Schrift eingestellt. So wird erreicht, dass IMMER ALLE Schriften denselben font haben. Sollten später z.B, Überschriften davon abweichen, müssen sie gesondert definiert werden.
	Und einige Eigenschaften werden global definiert, um sie später nicht immer wieder einsetzen zu müssen.
	Dies ist selbstverständlich komplett optional und kann jederzeit gelöscht oder verändert werden.
*/
*
{
/* margin definiert den Aussenabstand einer Box */	
	margin:0px;	
/* padding definiert den Innenabstand einer Box */
	padding:0px;
/* font-family definiert die Schriftart */	
	font-family:sans-serif;
/* position:relative definiert die Startposition einer Box */	
	position:relative;
/*  box-sizing:border-box definiert das padding und border NICHT zu Breite und Höhe dazu addiert werden  */	
	box-sizing:border-box;
}

html,body
{
	height:100%;
}


@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500;700&family=Inter:wght@400;500;700;800&display=swap');

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #1e1e2e 0%, #2a2a3a 100%);
    color: #f8f9fa;
    min-height: 100vh;
}

h1, h2, h3, h4 {
    font-family: 'Fira Code', monospace;
}

.code-block {
    background: #272833;
    border-radius: 12px;
    box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1);
}

.version-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
    background: linear-gradient(#2b2b40, #1e1e2e) padding-box, 
                linear-gradient(135deg, #4361ee, #4cc9f0) border-box;
    border-radius: 12px;
}

.version-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0,0,0,0.2);
}

.result-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(0.98); }
    50% { transform: scale(1); }
    100% { transform: scale(0.98); }
}

/* Progress bar width classes */
.progress-width-0 {
    width: 0%;
}

/* We'll need to update this class via JavaScript instead of inline styles */
.progress-bar {
    transition: width 1s ease-in-out;
}

.recommender-form input:checked + label {
    border-color: #4cc9f0;
    background: rgba(76, 201, 240, 0.1);
}

.hidden {
    display: none;
}

/* Neue Styles für die Icons und Hervorhebungen */
.factors-icon-container {
    background-color: rgba(76, 201, 240, 0.3);
    color: #4cc9f0;
    padding: 0.75rem;
    border-radius: 0.75rem;
    margin-right: 1rem;
    box-shadow: 0 0 15px rgba(76, 201, 240, 0.4);
    width: 3.5rem;
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.factors-icon {
    color: #4cc9f0;
    font-size: 1.5rem;
}

.experimental-percentage {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
    font-size: 2rem;
}

/* MEDIA QUERY */
@media screen and (max-width:800px) {
    /* Bestehende Media Queries bleiben erhalten */
}
